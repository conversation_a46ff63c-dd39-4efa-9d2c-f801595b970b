# Kafka Troubleshooting Guide

## Issue: Cannot see all producer topics in Kafka UI

### Root Causes

1. **Topics not created yet** - Kafka topics are created only when messages are sent to them
2. **Kafka UI connectivity issues** - Network problems between Kafka UI and Kafka broker
3. **Timing issues** - Kafka UI starts before Kafka is fully ready

### Solutions

#### 1. Pre-create Topics

Run the topic creation script to ensure all topics exist:

**Windows (PowerShell):**

```powershell
.\scripts\create-kafka-topics.ps1
```

**Linux/Mac (Bash):**

```bash
chmod +x scripts/create-kafka-topics.sh
./scripts/create-kafka-topics.sh
```

#### 2. Use Docker Compose with Topic Initialization

The updated `docker-compose.local.yml` includes a `kafka-init` service that automatically creates all topics when you start the stack:

```bash
docker-compose -f docker-compose.local.yml up -d
```

#### 3. Manual Topic Creation

If scripts don't work, create topics manually:

```bash
# Connect to Kafka container
docker exec -it zact-kafka-local bash

# Create topics one by one
kafka-topics --bootstrap-server localhost:9092 --create --topic entity-create-request --partitions 3 --replication-factor 1
kafka-topics --bootstrap-server localhost:9092 --create --topic entity-create-response --partitions 3 --replication-factor 1
kafka-topics --bootstrap-server localhost:9092 --create --topic entity-batch-stream --partitions 3 --replication-factor 1
kafka-topics --bootstrap-server localhost:9092 --create --topic sync-info --partitions 3 --replication-factor 1
kafka-topics --bootstrap-server localhost:9092 --create --topic unified-backend-sync-completed --partitions 3 --replication-factor 1
kafka-topics --bootstrap-server localhost:9092 --create --topic unified-backend-sync-failed --partitions 3 --replication-factor 1
kafka-topics --bootstrap-server localhost:9092 --create --topic unified-backend-connection-status-changed --partitions 3 --replication-factor 1

# List all topics
kafka-topics --bootstrap-server localhost:9092 --list
```

### Expected Topics

Your application uses these topics:

#### Main Application Topics

- `entity-create-request` - Entity creation requests
- `entity-create-response` - Entity creation responses
- `entity-batch-stream` - Sync data batches
- `sync-info` - Sync started, completion and error messages

#### Accounting Platform Topics

- `unified-backend-sync-completed` - Sync completion events
- `unified-backend-sync-failed` - Sync failure events
- `unified-backend-connection-status-changed` - Connection status changes

### Verification Steps

1. **Check Kafka UI**: Visit http://localhost:8090
2. **Verify topics exist**: All 7 topics should be visible
3. **Check topic details**: Each topic should have 3 partitions and replication factor 1
4. **Test message flow**: Send test messages to verify connectivity

### Common Issues

#### Kafka UI shows "No clusters configured"

- Check if Kafka UI can reach Kafka broker at `kafka:29092`
- Verify Docker network connectivity
- Restart Kafka UI container

#### Topics disappear after restart

- Check if `KAFKA_AUTO_CREATE_TOPICS_ENABLE` is set to `true`
- Verify topic retention settings
- Check Kafka logs for errors

#### Cannot connect to Kafka from application

- Verify `KAFKA_BROKERS` environment variable
- Check if application and Kafka are on same Docker network
- Test connectivity: `telnet kafka 29092`

### Monitoring Commands

```bash
# Check Kafka logs
docker logs zact-kafka-local

# Check Kafka UI logs
docker logs zact-kafka-ui-local

# List consumer groups
docker exec zact-kafka-local kafka-consumer-groups --bootstrap-server localhost:9092 --list

# Check topic details
docker exec zact-kafka-local kafka-topics --bootstrap-server localhost:9092 --describe --topic entity-create-request
```

### Performance Tuning

For production environments, consider:

- Increasing partition count for high-throughput topics
- Adjusting retention policies
- Configuring appropriate replication factors
- Setting up monitoring and alerting

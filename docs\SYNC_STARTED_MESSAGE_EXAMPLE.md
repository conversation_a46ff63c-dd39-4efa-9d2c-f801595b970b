# Sync Started Kafka Message Example

## Overview
When a QBO connection is established and background sync starts, a message is sent to the `sync-info` topic with all integration details.

## Topic
- **Topic Name**: `sync-info` (KAFKA_TOPICS.SYNC_COMPLETION_RESPONSE)
- **Message Type**: EVENT
- **Operation**: SYNC_STARTED

## Message Structure

```json
{
  "messageId": "550e8400-e29b-41d4-a716-446655440000",
  "correlationId": "550e8400-e29b-41d4-a716-446655440001",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "source": "UNIFIED_BACKEND",
  "destination": "ZACT_APP",
  "messageType": "EVENT",
  "erpSystem": "QBO",
  "entityOperation": {
    "entityType": "all",
    "operation": "SYNC_STARTED"
  },
  "securityContext": {
    "connectionId": "conn-123e4567-e89b-12d3-a456-************"
  },
  "payload": {
    "syncType": "FULL_SYNC",
    "context": "reconnected",
    "connectionDetails": {
      "connectionId": "conn-123e4567-e89b-12d3-a456-************",
      "zactCompanyId": "zact-org-456",
      "accountingPlatformType": "qbo",
      "externalCompanyId": "*********",
      "companyName": "Acme Corporation",
      "connectionStatus": "ACTIVE",
      "lastConnectedAt": "2024-01-15T10:30:00.000Z",
      "scheduledJobs": null,
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    },
    "message": "Background full sync started for Acme Corporation"
  },
  "status": {
    "code": "200",
    "message": "Sync started successfully"
  }
}
```

## Field Descriptions

### Message Headers
- `messageId`: Unique identifier for this message
- `correlationId`: Correlation ID for tracking related messages
- `timestamp`: ISO timestamp when message was created
- `source`: Always "UNIFIED_BACKEND" for sync started messages
- `destination`: Always "ZACT_APP" for sync started messages
- `messageType`: Always "EVENT" for sync started messages
- `erpSystem`: Always "QBO" for QuickBooks Online

### Entity Operation
- `entityType`: Always "all" for sync started messages (affects all entities)
- `operation`: Always "SYNC_STARTED" for these messages

### Security Context
- `connectionId`: The accounting platform integration ID

### Payload
- `syncType`: Type of sync operation ("FULL_SYNC" or "INCREMENTAL_SYNC")
- `context`: Optional context (e.g., "reconnected" for reconnection scenarios)
- `connectionDetails`: Complete integration record details
- `message`: Human-readable description of the sync operation

### Connection Details
All fields from the AccountingPlatformIntegration model:
- `connectionId`: Primary key (UUID)
- `zactCompanyId`: Zact organization ID
- `accountingPlatformType`: Platform type (e.g., "qbo")
- `externalCompanyId`: QBO company/realm ID
- `companyName`: Company name from QBO
- `connectionStatus`: Current status ("ACTIVE", "INACTIVE", etc.)
- `lastConnectedAt`: Last connection timestamp
- `scheduledJobs`: JSON field for scheduled job configuration
- `createdAt`: Record creation timestamp
- `updatedAt`: Record last update timestamp

## When This Message Is Sent

1. **New QBO Connection**: When a new QBO integration is created and sync starts
2. **Reconnection**: When an inactive integration is reactivated
3. **Manual Sync**: When sync is manually triggered (if implemented)

## Message Flow

1. QBO OAuth callback completes successfully
2. Integration record is created/updated in database
3. Sync started message is sent to Kafka (`sync-info` topic)
4. Background sync process begins
5. Additional messages follow during sync (data batches, completion, etc.)

## Error Handling

- If Kafka message sending fails, it's logged but doesn't break the main flow
- The sync process continues even if the notification message fails
- Error details are logged for debugging purposes

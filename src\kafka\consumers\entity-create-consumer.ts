import baseConsumer from "./base-consumer";
import logger from "../../utils/logger";
import { postApiGateway } from "../../services/api-gateway";
import prisma from "../../config/db";
import baseProducer from "../producers/base-producer";

import ApiException from "../../utils/api-exception";

// Import interfaces
import {
  IUnifiedJournalEntry,
  IUnifiedVendor,
  IUnifiedBill,
  IUnifiedPayment,
} from "../../interfaces";

// Import mappers
import {
  mapUnifiedToQboJournalEntryRequest,
  mapQboJournalEntryToUnified,
} from "../../mappers/journal_entry_entity";
import {
  mapUnifiedToQboVendorRequest,
  mapQboVendorResponseToUnified,
} from "../../mappers/vendor_entity";
import {
  mapUnifiedToQboBillRequest,
  mapQboBillResponseToUnified,
} from "../../mappers/bill_entity";
import {
  mapUnifiedToQboPaymentRequest,
  mapQboPaymentResponseToUnified,
} from "../../mappers/payment_entity";

// Import repositories
import {
  JournalEntryRepository,
  VendorRepository,
  BillRepository,
  PaymentRepository,
} from "../../repositories";

// Import response handlers
import {
  createSuccessResponse,
  createErrorResponse,
} from "../../handlers/responseHandlers";

// Import constants
import { KAFKA_TOPICS } from "../../constants/kafkaTopics";
import { getValidQboToken } from "../../middlewares/tokenUtils";

// Import validators
import {
  validateBillRequest,
  validatePaymentRequest,
  validateJournalEntryRequest,
  validateVendorRequest,
} from "../../validators";

// Topic constants (deprecated - use KAFKA_TOPICS instead)
export const TOPICS = {
  ENTITY_CREATE_REQUEST: KAFKA_TOPICS.ENTITY_CREATE_REQUEST,
  ENTITY_CREATE_RESPONSE: KAFKA_TOPICS.ENTITY_CREATE_RESPONSE,
};

/**
 * Process API Gateway response and save to database
 */
const processApiGatewayResponse = async (
  apiResponse: any,
  entityType: string,
  connectionId: string
): Promise<any> => {
  try {
    logger.info(`Processing API Gateway response for ${entityType}`);

    // Switch case based on response key
    const responseKeys = Object.keys(apiResponse);
    const mainResponseKey = responseKeys.find(
      (key) =>
        key.toLowerCase().includes(entityType.toLowerCase()) ||
        key === "JournalEntry" ||
        key === "Bill" ||
        key === "BillPayment" ||
        key === "Vendor"
    );

    if (!mainResponseKey) {
      throw new Error(
        `No matching response key found for entity type: ${entityType}`
      );
    }

    const qboData = apiResponse[mainResponseKey];
    logger.info(`Found response key: ${mainResponseKey}`, qboData);

    // Map QBO response back to unified format and save to database
    switch (mainResponseKey.toLowerCase()) {
      case "journalentry":
        const unifiedJournalEntry = mapQboJournalEntryToUnified(qboData);
        return await JournalEntryRepository.save(
          unifiedJournalEntry,
          connectionId
        );

      case "bill":
        const unifiedBill = mapQboBillResponseToUnified(qboData);
        return await BillRepository.save(unifiedBill, connectionId);

      case "billpayment":
        const unifiedPayment = mapQboPaymentResponseToUnified(qboData);
        return await PaymentRepository.save(unifiedPayment, connectionId);

      case "vendor":
        const unifiedVendor = mapQboVendorResponseToUnified(qboData);
        return await VendorRepository.save(unifiedVendor, connectionId);

      default:
        logger.warn(`Unhandled response key: ${mainResponseKey}`);
        return null;
    }
  } catch (error) {
    logger.error(`Error processing API Gateway response:`, error);
    throw error;
  }
};

// Response creation functions are now imported from handlers/responseHandlers.ts

// Entity creation handler
const handleEntityCreateRequest = async (
  message: any,
  messageId: string
): Promise<any> => {
  try {
    logger.info(`Processing entity creation request: ${messageId}`);

    // Extract message components

    const { payload, securityContext, erpSystem, entityOperation } = message;
    const entityType = entityOperation?.entityType?.toLowerCase();
    const erpSystemLower = erpSystem?.toLowerCase();

    logger.info(`Entity type: ${entityType}, ERP system: ${erpSystemLower}`);

    // Validate payload is array
    if (!Array.isArray(payload)) {
      logger.error("Payload must be an array of objects");
      return {
        error: {
          message: "Payload must be an array of objects",
          code: "INVALID_PAYLOAD_FORMAT",
        },
      };
    }

    // Get connection details
    const connection = await prisma.accountingPlatformIntegration.findFirst({
      where: {
        zactCompanyId: securityContext.tenantId,
        accountingPlatformType: erpSystemLower,
        connectionStatus: "ACTIVE",
      },
    });

    if (!connection) {
      logger.error(
        `No active connection found for tenant: ${securityContext.tenantId}`
      );
      return {
        error: {
          message: "No active accounting platform connection found",
          code: "CONNECTION_NOT_FOUND",
        },
      };
    }

    const { accessToken } = await getValidQboToken(connection.id);

    if (!accessToken) {
      logger.error(
        `Access token not available for connection ID: ${connection.id}`
      );
      return {
        error: {
          message: "Authentication access token missing",
          code: "AUTHENTICATION_TOKEN_MISSING",
        },
      };
    }

    const results: any[] = [];

    // Process each item in the payload array
    for (let i = 0; i < payload.length; i++) {
      const item = payload[i];
      logger.info(
        `Processing item ${i + 1}/${payload.length} for entity: ${entityType}`
      );

      // Validate the item first
      let validation: { isValid: boolean; errors: string[] };

      switch (entityType) {
        case "bill":
          validation = validateBillRequest(item);
          break;
        case "payment":
          validation = validatePaymentRequest(item);
          break;
        case "journalentry":
          validation = validateJournalEntryRequest(item);
          break;
        case "vendor":
          validation = validateVendorRequest(item);
          break;
        default:
          validation = {
            isValid: false,
            errors: [`Unsupported entity type: ${entityType}`],
          };
      }

      // Send error response if validation fails
      if (!validation.isValid) {
        const errorResponse = createErrorResponse(
          message,
          entityType,
          "VALIDATION_ERROR",
          `Validation failed for ${entityType}`,
          { validationErrors: validation.errors },
          item
        );

        await baseProducer.sendMessage(
          KAFKA_TOPICS.ENTITY_CREATE_RESPONSE,
          errorResponse,
          connection.id
        );

        logger.info(
          `Sent validation error response for ${entityType} item ${i + 1}`
        );

        results.push({
          index: i,
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: "Validation failed",
            details: validation.errors,
          },
        });
        continue; // Continue processing next item
      }

      // Map to QBO format based on ERP system and entity type
      let mappedRequest: any;

      switch (erpSystemLower) {
        case "qbo":
          switch (entityType) {
            case "bill":
              const unifiedBill: Partial<IUnifiedBill> = {
                ...item,
                domain: connection.accountingPlatformType,
              };
              mappedRequest = mapUnifiedToQboBillRequest(unifiedBill);

              break;

            case "payment":
              const unifiedPayment: Partial<IUnifiedPayment> = {
                ...item,
                domain: connection.accountingPlatformType,
              };
              mappedRequest = mapUnifiedToQboPaymentRequest(unifiedPayment);
              break;

            case "journalentry":
              const unifiedJournalEntry: Partial<IUnifiedJournalEntry> = {
                ...item,
                domain: connection.accountingPlatformType,
              };
              mappedRequest =
                mapUnifiedToQboJournalEntryRequest(unifiedJournalEntry);
              break;

            case "vendor":
              const unifiedVendor: Partial<IUnifiedVendor> = {
                ...item,
                domain: connection.accountingPlatformType,
              };
              mappedRequest = mapUnifiedToQboVendorRequest(unifiedVendor);
              break;

            default:
              throw new Error(`Unsupported entity type: ${entityType} for QBO`);
          }
          break;

        default:
          throw new Error(`Unsupported ERP system: ${erpSystemLower}`);
      }

      logger.info(
        `Mapped ${entityType} request for ${erpSystemLower.toUpperCase()}:`,
        JSON.stringify(mappedRequest, null, 2)
      );

      try {
        // Call API Gateway
        const apiResponse = await postApiGateway(
          erpSystemLower,
          entityType,
          accessToken,
          connection.externalCompanyId!,
          connection.id,
          mappedRequest
        );

        logger.info(
          `API Gateway response for ${entityType} ${i + 1}:`,
          JSON.stringify(apiResponse, null, 2)
        );

        // Process API Gateway response and save to database
        const savedEntity = await processApiGatewayResponse(
          apiResponse.data,
          entityType,
          connection.id
        );

        // Send success response
        const successResponse = createSuccessResponse(
          message,
          entityType,
          savedEntity,
          item
        );

        await baseProducer.sendMessage(
          TOPICS.ENTITY_CREATE_RESPONSE,
          successResponse,
          connection.id
        );

        logger.info(`Sent success response for ${entityType} item ${i + 1}`);

        results.push({
          index: i,
          success: true,
          data: apiResponse,
          savedEntity: savedEntity,
          message: `${entityType} created successfully`,
        });
      } catch (itemError) {
        logger.error(
          `Error processing ${entityType} item ${i + 1}:`,
          itemError
        );

        // Handle API exceptions with detailed error information
        let errorCode = "PROCESSING_ERROR";
        let errorMessage = `Failed to create ${entityType}`;
        let errorDetails: any = {};

        if (itemError instanceof ApiException) {
          errorCode = "API_ERROR";
          // Use the specific error description instead of generic message
          errorMessage =
            itemError.errorDescription ||
            itemError.message ||
            "API Gateway error occurred";
          errorDetails = {
            status: itemError.status,
            code: itemError.code,
            errorDescription: itemError.errorDescription,
            message: itemError.message,
          };
        } else if (itemError instanceof Error) {
          if (
            itemError.message.includes("database") ||
            itemError.message.includes("Database")
          ) {
            errorCode = "DATABASE_ERROR";
            errorMessage = "Database operation failed";
          }
          errorDetails = { error: itemError.message };
        } else {
          errorDetails = { error: String(itemError) };
        }

        const errorResponse = createErrorResponse(
          message,
          entityType,
          errorCode,
          errorMessage,
          errorDetails,
          item
        );

        await baseProducer.sendMessage(
          KAFKA_TOPICS.ENTITY_CREATE_RESPONSE,
          errorResponse,
          connection.id
        );

        logger.info(`Sent error response for ${entityType} item ${i + 1}`);

        results.push({
          index: i,
          success: false,
          error: {
            message: errorMessage,
            details: errorDetails,
            code: errorCode,
          },
        });
      }
    }

    // Return summary results
    const successCount = results.filter((r) => r.success).length;
    const failureCount = results.filter((r) => !r.success).length;

    logger.info(
      `Entity creation completed: ${successCount} successful, ${failureCount} failed`
    );

    // Log summary but don't return response to prevent base consumer auto-response
    logger.info(
      `Entity creation processing completed: ${successCount} successful, ${failureCount} failed`
    );
    logger.info(
      `Individual responses sent for all ${payload.length} entities to response topic`
    );

    // Return null to prevent base consumer from sending additional response
    return null;
  } catch (error) {
    logger.error(`Error processing entity creation request:`, error);
    return {
      error: {
        message: "Internal server error",
        details: error,
        code: "INTERNAL_ERROR",
      },
    };
  }
};

// Initialize the consumer
export const initializeEntityCreateConsumer = async (): Promise<void> => {
  try {
    // Initialize base consumer first
    await baseConsumer.initialize();

    await baseConsumer.subscribe(
      KAFKA_TOPICS.ENTITY_CREATE_REQUEST,
      handleEntityCreateRequest,                                                                                                                                                          
      KAFKA_TOPICS.ENTITY_CREATE_RESPONSE
    );
    // Logging moved to main kafka init
  } catch (error) {
    logger.error(`❌ Entity create consumer initialization failed: ${error}`);
    throw error;
  }
};

export default {
  initializeEntityCreateConsumer,
  TOPICS,
};

import { ConnectionStatus, SyncOperationType } from "@prisma/client";
import { envConfig } from "../../config/config";
import prisma from "../../config/db";
import ApiException from "../../utils/api-exception";
import logger from "../../utils/logger";
import { ErrorCode, HttpStatus } from "../../utils/response";
import {
  createAuthData,
  exchangeCodeForToken,
  checkExistingIntegrations,
  upsertIntegration,
} from "../../utils/auth-utils";
import {
  startQboBackgroundSync,
  fetchQboCompanyInfo,
  handleQboInactiveIntegrationReconnection,
} from "../../utils/qbo-utils";
import baseProducer from "../../kafka/producers/base-producer";
import { KAFKA_TOPICS } from "../../constants/kafkaTopics";
import { v4 as uuidv4 } from "uuid";
import {
  MessageSource,
  MessageDestination,
  MessageType,
  ERPSystem,
  EntityType,
} from "../../interfaces/kafkaMessageInterface";
import axios from "axios";

/**
 * Determine QBO domain/environment from various sources
 */
const determineQboDomain = (integration?: any): string => {
  // First, check if domain is stored in authentication data
  if (integration?.authentication?.domain) {
    return integration.authentication.domain;
  }

  // Check environment configuration
  if (envConfig.qbo.environment === "production") {
    return "production";
  }

  // Check base URL configuration
  if (envConfig.qbo.baseUrl) {
    if (envConfig.qbo.baseUrl.includes("sandbox")) {
      return "sandbox";
    } else if (envConfig.qbo.baseUrl.includes("quickbooks.api.intuit.com")) {
      return "production";
    }
  }

  // Default to sandbox for safety
  return "sandbox";
};

/**
 * Revoke QBO access token using QBO's revoke API
 */
const revokeQboToken = async (
  accessToken: string,
  refreshToken: string,
  domain: string
): Promise<void> => {
  try {
    const { clientId, clientSecret } = envConfig.qbo;

    // Determine the revoke URL based on domain
    let revokeUrl: string;

    switch (domain.toLowerCase()) {
      case "sandbox":
      case "sandbox-quickbooks.api.intuit.com":
        revokeUrl = "https://developer.api.intuit.com/v2/oauth2/tokens/revoke";
        break;
      case "production":
      case "quickbooks.api.intuit.com":
        revokeUrl = "https://developer.api.intuit.com/v2/oauth2/tokens/revoke";
        break;
      default:
        // Fallback to configured revoke URL
        revokeUrl = envConfig.qbo.revokeUrl;
        logger.warn(
          `Unknown domain ${domain}, using default revoke URL: ${revokeUrl}`
        );
        break;
    }

    // Prepare the revoke request for refresh token (this also revokes access token)
    const params = new URLSearchParams({
      token: refreshToken,
    });

    const response = await axios.post(revokeUrl, params, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${Buffer.from(
          `${clientId}:${clientSecret}`
        ).toString("base64")}`,
        Accept: "application/json",
      },
      timeout: 10000, // 10 second timeout
    });

    if (response.status === 200) {
      logger.info("Successfully revoked QBO tokens", {
        domain,
        revokeUrl,
        status: response.status,
      });
    } else {
      logger.warn("QBO token revocation returned non-200 status", {
        domain,
        revokeUrl,
        status: response.status,
        statusText: response.statusText,
      });
    }
  } catch (error: any) {
    logger.error("Failed to revoke QBO tokens", {
      error: error.message,
      domain,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
    });

    // Don't throw error to avoid breaking the disconnection flow
    // Token revocation failure shouldn't prevent local disconnection
  }
};

/**
 * Send sync started message to Kafka
 */
const sendSyncStartedMessage = async (
  integration: any,
  syncOperationType: SyncOperationType
): Promise<void> => {
  try {
    const correlationId = uuidv4();
    const messageId = uuidv4();

    const syncStartedMessage = {
      messageId,
      correlationId,
      timestamp: new Date().toISOString(),
      source: MessageSource.UNIFIED_BACKEND,
      destination: MessageDestination.ZACT_APP,
      messageType: MessageType.EVENT,
      erpSystem: ERPSystem.QBO,
      entityOperation: {
        entityType: EntityType.ALL,
        operation: "SYNC_STARTED",
      },
      securityContext: {
        connectionId: integration.id,
      },
      payload: {
        syncType: syncOperationType,
        connectionDetails: {
          connectionId: integration.id,
          zactCompanyId: integration.zactCompanyId,
          accountingPlatformType: integration.accountingPlatformType,
          externalCompanyId: integration.externalCompanyId,
          companyName: integration.companyName,
          connectionStatus: integration.connectionStatus,
          lastConnectedAt: integration.lastConnectedAt,
          scheduledJobs: integration.scheduledJobs,
          createdAt: integration.createdAt,
          updatedAt: integration.updatedAt,
        },
        message: `Background ${syncOperationType
          .toLowerCase()
          .replace("_", " ")} started for ${integration.companyName}`,
      },
      status: {
        code: "200",
        message: "Sync started successfully",
      },
    };

    await baseProducer.sendMessage(
      KAFKA_TOPICS.SYNC_COMPLETION_RESPONSE,
      syncStartedMessage,
      integration.id
    );

    logger.info(
      `Sync started message sent to Kafka for connection ${integration.id}`,
      {
        correlationId,
        messageId,
        syncType: syncOperationType,
        companyName: integration.companyName,
        topic: KAFKA_TOPICS.SYNC_COMPLETION_RESPONSE,
      }
    );
  } catch (error) {
    logger.error(
      `Failed to send sync started message to Kafka for connection ${integration.id}`,
      {
        error: error,
        syncType: syncOperationType,
        companyName: integration.companyName,
      }
    );
    // Don't throw error to avoid breaking the main flow
  }
};

const getQboAuthUrl = (orgId: string) => {
  const { clientId, redirectUri, authUrl } = envConfig.qbo;

  const scopes = [
    "com.intuit.quickbooks.accounting",
    "openid",
    "profile",
    "email",
  ].join(" ");

  const params = new URLSearchParams({
    client_id: clientId,
    response_type: "code",
    scope: scopes,
    redirect_uri: redirectUri,
    state: orgId, // Using orgId as state for simplicity
  });

  const genratedAuthUrl = `${authUrl}?${params.toString()}`;
  //do res.send on generated url
  return {
    data: genratedAuthUrl,
    message: "QBO authorization URL generated successfully",
  };
};

/**
 * Handles the OAuth callback from QBO
 * @param code - The authorization code received from QBO
 * @returns Access token information including access_token, refresh_token, etc.
 */

/**
 * QBO-specific interface for auth response
 */
interface QboAuthResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  id_token?: string;
  scope?: string;
}

const handleQboCallback = async (
  code: string,
  realmId: string,
  state: string // orgId is passed as state
) => {
  try {
    const { clientId, clientSecret, redirectUri, tokenUrl, baseUrl } =
      envConfig.qbo;
    const orgId = state; // orgId was passed as state

    // Validate required configuration
    if (!clientId || !clientSecret || !redirectUri || !tokenUrl || !baseUrl) {
      throw new ApiException({
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        code: ErrorCode.INTERNAL_ERROR,
        message: "QBO configuration is incomplete",
      });
    }

    // Step 1: Exchange authorization code for access token
    const { access_token, refresh_token, expires_in } =
      await exchangeCodeForToken(
        code,
        clientId,
        clientSecret,
        redirectUri,
        tokenUrl
      );

    // Step 2: Fetch company information
    const companyName = await fetchQboCompanyInfo(
      access_token,
      realmId,
      baseUrl
    );

    // Step 3: Check for existing integrations and handle conflicts
    const { existingIntegration, existingIntegrationWithSameExternalId } =
      await checkExistingIntegrations(orgId, realmId, "qbo");

    // Step 4: Handle case where QBO company is already connected to a different Zact org
    if (
      existingIntegrationWithSameExternalId &&
      existingIntegrationWithSameExternalId.connectionStatus ===
        ConnectionStatus.ACTIVE
    ) {
      throw new ApiException({
        status: HttpStatus.CONFLICT,
        code: ErrorCode.CONFLICT,
        message: `This QuickBooks company is already actively connected to a different organization. Please disconnect from the other organization first.`,
      });
    }

    // Step 5: Handle case where we're reconnecting an inactive integration
    if (
      existingIntegrationWithSameExternalId &&
      existingIntegrationWithSameExternalId.connectionStatus ===
        ConnectionStatus.INACTIVE
    ) {
      return await handleQboInactiveIntegrationReconnection(
        existingIntegrationWithSameExternalId.id,
        orgId,
        access_token,
        refresh_token,
        expires_in,
        realmId,
        companyName
      );
    }

    // Step 6: Determine sync type based on whether this is a new or existing connection
    // or if there are any entity sync states with null lastSuccessfulSyncAt
    let shouldRunInitialSync = !existingIntegration;

    if (!shouldRunInitialSync && existingIntegration) {
      // Check if there are any entity sync states with null lastSuccessfulSyncAt
      const entitySyncStates = await prisma.entitySyncState.findMany({
        where: {
          connectionId: existingIntegration.id,
          lastSuccessfulSyncAt: null,
        },
      });

      // If we found any entity sync states with null lastSuccessfulSyncAt, run a complete sync
      shouldRunInitialSync = entitySyncStates.length > 0;

      if (shouldRunInitialSync) {
        logger.info(
          `Found entity sync states with null lastSuccessfulSyncAt, running complete sync`,
          {
            orgId,
            realmId,
            entitySyncStatesCount: entitySyncStates.length,
          }
        );
      }
    }

    const syncOperationType = shouldRunInitialSync
      ? SyncOperationType.FULL_SYNC
      : SyncOperationType.INCREMENTAL_SYNC;

    logger.info(`Sync operation type: ${syncOperationType}`, {
      shouldRunInitialSync,
      orgId,
      realmId,
    });

    // Step 7: Create or update the integration with domain information
    const currentDomain = determineQboDomain();
    const authData = createAuthData(
      access_token,
      refresh_token,
      expires_in,
      currentDomain
    );
    const integration = await upsertIntegration(
      orgId,
      realmId,
      companyName,
      authData,
      "qbo"
    );

    // Step 8: Send sync started message to Kafka
    await sendSyncStartedMessage(integration, syncOperationType);

    // Step 9: Initiate background sync
    startQboBackgroundSync(
      integration.id,
      access_token,
      realmId,
      syncOperationType
    );

    // Step 10: Return success response
    const message = shouldRunInitialSync
      ? "Successfully connected to QBO and tokens stored. Full sync started in background."
      : "Successfully connected to QBO and tokens stored. Incremental sync started in background for updated data.";

    return {
      message,
      data: {
        connectionId: integration.id,
        realmId: integration.externalCompanyId,
        erpSystem: "qbo",
      },
    };
  } catch (error) {
    logger.error("Error in QBO callback handler", {
      error,
      realmId,
      orgId: state,
    });

    if (error instanceof ApiException) {
      throw error;
    }

    throw new ApiException({
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      code: ErrorCode.INTERNAL_ERROR,
      message: "Failed to process QBO callback",
    });
  }
};

/**
 * Disconnects a QBO integration for a given organization
 */
const disconnectQbo = async (zactOrgId: string) => {
  try {
    // Validate zactOrgId
    if (!zactOrgId) {
      throw new ApiException({
        status: HttpStatus.BAD_REQUEST,
        code: ErrorCode.FORBIDDEN,
        message: "Organization ID is required",
      });
    }

    // Find the integration to disconnect
    const integration = await prisma.accountingPlatformIntegration.findUnique({
      where: {
        zactCompanyId: zactOrgId,
      },
    });

    if (!integration) {
      throw new ApiException({
        status: HttpStatus.NOT_FOUND,
        code: ErrorCode.NOT_FOUND,
        message: "QBO integration not found for the given organization",
      });
    }

    // Extract authentication details before revoking
    const auth = integration.authentication as any;
    const accessToken = auth?.accessToken;
    const refreshToken = auth?.refreshToken;

    // Determine domain using utility function
    const domain = determineQboDomain(integration);

    // Revoke tokens at QBO if we have valid tokens
    if (accessToken && refreshToken) {
      logger.info(`Revoking QBO tokens for integration ${integration.id}`, {
        integrationId: integration.id,
        orgId: zactOrgId,
        domain,
        companyName: integration.companyName,
      });

      await revokeQboToken(accessToken, refreshToken, domain);
    } else {
      logger.warn(
        `No valid tokens found for integration ${integration.id}, skipping token revocation`,
        {
          integrationId: integration.id,
          orgId: zactOrgId,
          hasAccessToken: !!accessToken,
          hasRefreshToken: !!refreshToken,
        }
      );
    }

    // Update the integration to mark it as disconnected
    await prisma.accountingPlatformIntegration.update({
      where: {
        id: integration.id,
      },
      data: {
        connectionStatus: ConnectionStatus.INACTIVE,
        authentication: {
          accessToken: null,
          refreshToken: null,
          expiresAt: null,
          lastRefreshedAt: null,
        },
      },
    });

    logger.info(
      `Successfully disconnected QBO integration for organization ${zactOrgId}`,
      {
        integrationId: integration.id,
        orgId: zactOrgId,
      }
    );

    return {
      message: "Successfully disconnected from QBO",
    };
  } catch (error) {
    logger.error(
      `Error disconnecting QBO integration for organization ${zactOrgId}`,
      { error }
    );

    if (error instanceof ApiException) {
      throw error;
    }

    throw new ApiException({
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      code: ErrorCode.INTERNAL_ERROR,
      message: "Failed to disconnect from QBO",
    });
  }
};

export const qboAuthServices = {
  getQboAuthUrl,
  handleQboCallback,
  disconnectQbo,
};

import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { SyncOperationType, ConnectionStatus } from '@prisma/client';

// Mock the dependencies
jest.mock('../src/kafka/producers/base-producer');
jest.mock('../src/utils/logger');

import baseProducer from '../src/kafka/producers/base-producer';
import logger from '../src/utils/logger';

// Import the function we want to test (we'll need to export it first)
// For now, let's create a test version of the function

const mockIntegration = {
  id: 'test-connection-id-123',
  zactCompanyId: 'test-zact-company-456',
  accountingPlatformType: 'qbo',
  externalCompanyId: 'test-qbo-realm-789',
  companyName: 'Test Company Inc.',
  connectionStatus: ConnectionStatus.ACTIVE,
  lastConnectedAt: new Date('2024-01-15T10:30:00.000Z'),
  scheduledJobs: null,
  createdAt: new Date('2024-01-15T10:30:00.000Z'),
  updatedAt: new Date('2024-01-15T10:30:00.000Z'),
};

describe('Sync Started Message', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (baseProducer.sendMessage as jest.Mock).mockResolvedValue(undefined);
  });

  it('should send sync started message with correct structure for FULL_SYNC', async () => {
    // Mock the sendMessage function
    const mockSendMessage = baseProducer.sendMessage as jest.Mock;
    
    // Import and call the function (this would be the actual function call)
    // For testing purposes, let's simulate what the message should look like
    const expectedMessage = {
      messageId: expect.any(String),
      correlationId: expect.any(String),
      timestamp: expect.any(String),
      source: 'UNIFIED_BACKEND',
      destination: 'ZACT_APP',
      messageType: 'EVENT',
      erpSystem: 'QBO',
      entityOperation: {
        entityType: 'all',
        operation: 'SYNC_STARTED',
      },
      securityContext: {
        connectionId: mockIntegration.id,
      },
      payload: {
        syncType: SyncOperationType.FULL_SYNC,
        connectionDetails: {
          connectionId: mockIntegration.id,
          zactCompanyId: mockIntegration.zactCompanyId,
          accountingPlatformType: mockIntegration.accountingPlatformType,
          externalCompanyId: mockIntegration.externalCompanyId,
          companyName: mockIntegration.companyName,
          connectionStatus: mockIntegration.connectionStatus,
          lastConnectedAt: mockIntegration.lastConnectedAt,
          scheduledJobs: mockIntegration.scheduledJobs,
          createdAt: mockIntegration.createdAt,
          updatedAt: mockIntegration.updatedAt,
        },
        message: `Background full sync started for ${mockIntegration.companyName}`,
      },
      status: {
        code: '200',
        message: 'Sync started successfully',
      },
    };

    // Simulate the function call
    // await sendSyncStartedMessage(mockIntegration, SyncOperationType.FULL_SYNC);

    // For now, let's just verify the expected message structure
    expect(expectedMessage.source).toBe('UNIFIED_BACKEND');
    expect(expectedMessage.destination).toBe('ZACT_APP');
    expect(expectedMessage.messageType).toBe('EVENT');
    expect(expectedMessage.erpSystem).toBe('QBO');
    expect(expectedMessage.entityOperation.operation).toBe('SYNC_STARTED');
    expect(expectedMessage.payload.syncType).toBe(SyncOperationType.FULL_SYNC);
    expect(expectedMessage.payload.connectionDetails.connectionId).toBe(mockIntegration.id);
    expect(expectedMessage.payload.connectionDetails.companyName).toBe(mockIntegration.companyName);
  });

  it('should send sync started message with correct structure for INCREMENTAL_SYNC', async () => {
    const expectedMessage = {
      payload: {
        syncType: SyncOperationType.INCREMENTAL_SYNC,
        message: `Background incremental sync started for ${mockIntegration.companyName}`,
      },
    };

    expect(expectedMessage.payload.syncType).toBe(SyncOperationType.INCREMENTAL_SYNC);
    expect(expectedMessage.payload.message).toContain('incremental sync');
  });

  it('should include context when provided', async () => {
    const context = 'reconnected';
    const expectedMessage = {
      payload: {
        context: context,
        message: `${context} Background full sync started for ${mockIntegration.companyName}`,
      },
    };

    expect(expectedMessage.payload.context).toBe(context);
    expect(expectedMessage.payload.message).toContain(context);
  });

  it('should include all required integration fields', () => {
    const requiredFields = [
      'connectionId',
      'zactCompanyId', 
      'accountingPlatformType',
      'externalCompanyId',
      'companyName',
      'connectionStatus',
      'lastConnectedAt',
      'scheduledJobs',
      'createdAt',
      'updatedAt'
    ];

    requiredFields.forEach(field => {
      expect(mockIntegration).toHaveProperty(field);
    });
  });
});

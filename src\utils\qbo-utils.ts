import axios from "axios";
import { SyncOperationType } from "@prisma/client";
import ApiException from "./api-exception";
import logger from "./logger";
import { ErrorCode, HttpStatus } from "./response";
import { executeFullSyncWithMessaging } from "../services/kafka-message/index";
import { createAuthData } from "./auth-utils";
import baseProducer from "../kafka/producers/base-producer";
import { KAFKA_TOPICS } from "../constants/kafkaTopics";
import { v4 as uuidv4 } from "uuid";
import {
  MessageSource,
  MessageDestination,
  MessageType,
  ERPSystem,
  EntityType,
} from "../interfaces/kafkaMessageInterface";

/**
 * Send sync started message to Kafka
 */
const sendSyncStartedMessage = async (
  integration: any,
  syncOperationType: SyncOperationType,
  context: string = ""
): Promise<void> => {
  try {
    const correlationId = uuidv4();
    const messageId = uuidv4();
    const contextPrefix = context ? `${context} ` : "";

    const syncStartedMessage = {
      messageId,
      correlationId,
      timestamp: new Date().toISOString(),
      source: MessageSource.UNIFIED_BACKEND,
      destination: MessageDestination.ZACT_APP,
      messageType: MessageType.EVENT,
      erpSystem: ERPSystem.QBO,
      entityOperation: {
        entityType: EntityType.ALL,
        operation: "SYNC_STARTED",
      },
      securityContext: {
        connectionId: integration.id,
      },
      payload: {
        syncType: syncOperationType,
        context: contextPrefix.trim(),
        connectionDetails: {
          connectionId: integration.id,
          zactCompanyId: integration.zactCompanyId,
          accountingPlatformType: integration.accountingPlatformType,
          externalCompanyId: integration.externalCompanyId,
          companyName: integration.companyName,
          connectionStatus: integration.connectionStatus,
          lastConnectedAt: integration.lastConnectedAt,
          scheduledJobs: integration.scheduledJobs,
          createdAt: integration.createdAt,
          updatedAt: integration.updatedAt,
        },
        message: `${contextPrefix}Background ${syncOperationType
          .toLowerCase()
          .replace("_", " ")} started for ${integration.companyName}`,
      },
      status: {
        code: "200",
        message: "Sync started successfully",
      },
    };

    await baseProducer.sendMessage(
      KAFKA_TOPICS.SYNC_COMPLETION_RESPONSE,
      syncStartedMessage,
      integration.id
    );

    logger.info(
      `${contextPrefix}Sync started message sent to Kafka for connection ${integration.id}`,
      {
        correlationId,
        messageId,
        syncType: syncOperationType,
        companyName: integration.companyName,
        topic: KAFKA_TOPICS.SYNC_COMPLETION_RESPONSE,
        context: contextPrefix.trim(),
      }
    );
  } catch (error) {
    const contextPrefix = context ? `${context} ` : "";
    logger.error(
      `Failed to send ${contextPrefix}sync started message to Kafka for connection ${integration.id}`,
      {
        error: error,
        syncType: syncOperationType,
        companyName: integration.companyName,
        context: contextPrefix.trim(),
      }
    );
    // Don't throw error to avoid breaking the main flow
  }
};

/**
 * Helper function to start a background sync process for QBO
 * @param connectionId - The connection ID to sync
 * @param accessToken - The access token for QBO API authentication
 * @param realmId - The QBO realm ID
 * @param syncType - The type of sync operation to perform
 * @param context - Optional context information for logging
 */
export function startQboBackgroundSync(
  connectionId: string,
  accessToken: string,
  realmId: string,
  syncType: SyncOperationType,
  context: string = ""
): void {
  const contextPrefix = context ? `${context} ` : "";

  // Start the sync process in the background
  (async () => {
    try {
      logger.info(
        `Starting ${syncType} sync for ${contextPrefix}QBO connection ${connectionId}`
      );

      await executeFullSyncWithMessaging(
        "qbo",
        connectionId,
        accessToken,
        realmId,
        syncType
      );

      logger.info(
        `${syncType} sync completed for ${contextPrefix}QBO connection ${connectionId}`
      );
    } catch (syncError) {
      logger.error(
        `Error during ${syncType} sync for ${contextPrefix}QBO connection ${connectionId}`,
        {
          error: syncError,
          connectionId,
          syncType,
        }
      );
    }
  })();
}

/**
 * Fetch company information from QBO
 * @param accessToken - QBO access token
 * @param realmId - QBO realm ID
 * @param baseUrl - QBO API base URL
 * @returns Company name
 */
export async function fetchQboCompanyInfo(
  accessToken: string,
  realmId: string,
  baseUrl: string
): Promise<string> {
  const response = await axios.get(
    `${baseUrl}/${realmId}/companyinfo/${realmId}`,
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json",
      },
    }
  );
  return response.data.CompanyInfo.CompanyName;
}

/**
 * Handle reconnection of an inactive QBO integration
 * @param integrationId - The integration ID
 * @param orgId - The organization ID
 * @param accessToken - The QBO access token
 * @param refreshToken - The QBO refresh token
 * @param expiresIn - Token expiration time in seconds
 * @param realmId - The QBO realm ID
 * @param companyName - The company name
 * @returns Response object with connection details
 */
export async function handleQboInactiveIntegrationReconnection(
  integrationId: string,
  orgId: string,
  accessToken: string,
  refreshToken: string,
  expiresIn: number,
  realmId: string,
  companyName: string
) {
  // Import prisma and ConnectionStatus here to avoid circular dependencies
  const { default: prisma } = require("../config/db");
  const { ConnectionStatus, SyncOperationType } = require("@prisma/client");

  // Update the existing inactive integration
  const authData = createAuthData(accessToken, refreshToken, expiresIn);
  const updatedIntegration = await prisma.accountingPlatformIntegration.update({
    where: {
      id: integrationId,
    },
    data: {
      zactCompanyId: orgId,
      connectionStatus: ConnectionStatus.ACTIVE,
      lastConnectedAt: new Date(),
      authentication: authData,
      companyName,
    },
  });

  // Send sync started message to Kafka
  await sendSyncStartedMessage(
    updatedIntegration,
    SyncOperationType.INCREMENTAL_SYNC,
    "reconnected"
  );

  // Start an instant sync for the reconnected integration
  startQboBackgroundSync(
    updatedIntegration.id,
    accessToken,
    realmId,
    SyncOperationType.INCREMENTAL_SYNC,
    "reconnected"
  );

  // Return success response
  return {
    message:
      "Successfully reconnected to QBO and updated organization association. Instant sync started in background.",
    data: {
      connectionId: integrationId,
      realmId,
      erpSystem: "qbo",
    },
  };
}
